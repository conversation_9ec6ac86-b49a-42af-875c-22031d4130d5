import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { Assessment, AssessmentType, AssessmentDifficulty, AssessmentStatus } from '../../entities/assessment.entity';
import { Question, QuestionType, QuestionDifficulty, QuestionCategory } from '../../entities/question.entity';
import { QuestionOption } from '../../entities/question-option.entity';
import { AssessmentAttempt, AttemptStatus } from '../../entities/assessment-attempt.entity';
import { AssessmentAnswer } from '../../entities/assessment-answer.entity';
import { User } from '../../entities/user.entity';
import { Course } from '../../entities/course.entity';
import { Unit } from '../../entities/unit.entity';

export interface CreateAssessmentDto {
  title: string;
  description?: string;
  type: AssessmentType;
  difficulty: AssessmentDifficulty;
  total_questions: number;
  time_limit_minutes: number;
  max_attempts?: number;
  passing_score?: number;
  randomize_questions?: boolean;
  randomize_options?: boolean;
  show_results_immediately?: boolean;
  show_correct_answers?: boolean;
  is_adaptive?: boolean;
  course_id?: string;
  unit_id?: string;
  available_from?: Date;
  available_until?: Date;
}

export interface CreateQuestionDto {
  question_text: string;
  type: QuestionType;
  difficulty: QuestionDifficulty;
  category: QuestionCategory;
  points?: number;
  explanation?: string;
  reference?: string;
  image_url?: string;
  tags?: string[];
  options: {
    option_text: string;
    is_correct: boolean;
    explanation?: string;
    partial_credit_percentage?: number;
  }[];
}

export interface StartAssessmentDto {
  assessment_id: string;
}

export interface SubmitAnswerDto {
  question_id: string;
  answer_data: any;
  confidence_level?: number;
  flagged_for_review?: boolean;
  time_spent_seconds?: number;
}

@Injectable()
export class AssessmentsService {
  constructor(
    @InjectRepository(Assessment)
    private readonly assessmentRepository: Repository<Assessment>,
    @InjectRepository(Question)
    private readonly questionRepository: Repository<Question>,
    @InjectRepository(QuestionOption)
    private readonly optionRepository: Repository<QuestionOption>,
    @InjectRepository(AssessmentAttempt)
    private readonly attemptRepository: Repository<AssessmentAttempt>,
    @InjectRepository(AssessmentAnswer)
    private readonly answerRepository: Repository<AssessmentAnswer>,
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
    @InjectRepository(Unit)
    private readonly unitRepository: Repository<Unit>,
  ) {}

  async createAssessment(createAssessmentDto: CreateAssessmentDto, creatorId: string): Promise<Assessment> {
    // Validate course and unit if provided
    if (createAssessmentDto.course_id) {
      const course = await this.courseRepository.findOne({
        where: { id: createAssessmentDto.course_id },
      });
      if (!course) {
        throw new NotFoundException('Course not found');
      }
    }

    if (createAssessmentDto.unit_id) {
      const unit = await this.unitRepository.findOne({
        where: { id: createAssessmentDto.unit_id },
      });
      if (!unit) {
        throw new NotFoundException('Unit not found');
      }
    }

    const assessment = this.assessmentRepository.create({
      ...createAssessmentDto,
      created_by: creatorId,
      status: AssessmentStatus.DRAFT,
    });

    return await this.assessmentRepository.save(assessment);
  }

  async findAll(filters: {
    course_id?: string;
    unit_id?: string;
    type?: AssessmentType;
    status?: AssessmentStatus;
    created_by?: string;
  } = {}) {
    const queryBuilder = this.assessmentRepository
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.course', 'course')
      .leftJoinAndSelect('assessment.unit', 'unit')
      .leftJoinAndSelect('assessment.creator', 'creator')
      .leftJoinAndSelect('assessment.questions', 'questions');

    if (filters.course_id) {
      queryBuilder.andWhere('assessment.course_id = :course_id', { course_id: filters.course_id });
    }

    if (filters.unit_id) {
      queryBuilder.andWhere('assessment.unit_id = :unit_id', { unit_id: filters.unit_id });
    }

    if (filters.type) {
      queryBuilder.andWhere('assessment.type = :type', { type: filters.type });
    }

    if (filters.status) {
      queryBuilder.andWhere('assessment.status = :status', { status: filters.status });
    }

    if (filters.created_by) {
      queryBuilder.andWhere('assessment.created_by = :created_by', { created_by: filters.created_by });
    }

    queryBuilder.orderBy('assessment.created_at', 'DESC');

    return await queryBuilder.getMany();
  }

  async findOne(id: string, userId?: string): Promise<Assessment> {
    const assessment = await this.assessmentRepository.findOne({
      where: { id },
      relations: [
        'course',
        'unit',
        'creator',
        'questions',
        'questions.options',
        'attempts',
      ],
    });

    if (!assessment) {
      throw new NotFoundException('Assessment not found');
    }

    // If user is provided, get their attempt history
    if (userId) {
      const userAttempts = await this.attemptRepository.find({
        where: { assessment_id: id, user_id: userId },
        order: { attempt_number: 'DESC' },
      });

      (assessment as any).user_attempts = userAttempts;
      (assessment as any).can_attempt = this.canUserAttempt(assessment, userAttempts);
    }

    return assessment;
  }

  async addQuestion(assessmentId: string, createQuestionDto: CreateQuestionDto, creatorId: string): Promise<Question> {
    const assessment = await this.assessmentRepository.findOne({
      where: { id: assessmentId },
    });

    if (!assessment) {
      throw new NotFoundException('Assessment not found');
    }

    // Check if user can modify this assessment
    if (assessment.created_by !== creatorId) {
      throw new ForbiddenException('Only the creator can modify this assessment');
    }

    const question = this.questionRepository.create({
      ...createQuestionDto,
      assessment_id: assessmentId,
      created_by: creatorId,
    });

    const savedQuestion = await this.questionRepository.save(question);

    // Create options
    if (createQuestionDto.options && createQuestionDto.options.length > 0) {
      const options = createQuestionDto.options.map((optionData, index) => 
        this.optionRepository.create({
          ...optionData,
          question_id: savedQuestion.id,
          order_index: index,
        })
      );

      await this.optionRepository.save(options);
    }

    return await this.questionRepository.findOne({
      where: { id: savedQuestion.id },
      relations: ['options'],
    });
  }

  async startAssessment(assessmentId: string, userId: string): Promise<AssessmentAttempt> {
    const assessment = await this.assessmentRepository.findOne({
      where: { id: assessmentId, status: AssessmentStatus.PUBLISHED },
      relations: ['questions'],
    });

    if (!assessment) {
      throw new NotFoundException('Assessment not found or not available');
    }

    // Check if assessment is available
    const now = new Date();
    if (assessment.available_from && now < assessment.available_from) {
      throw new BadRequestException('Assessment is not yet available');
    }

    if (assessment.available_until && now > assessment.available_until) {
      throw new BadRequestException('Assessment is no longer available');
    }

    // Check previous attempts
    const previousAttempts = await this.attemptRepository.find({
      where: { assessment_id: assessmentId, user_id: userId },
      order: { attempt_number: 'DESC' },
    });

    if (!this.canUserAttempt(assessment, previousAttempts)) {
      throw new BadRequestException('Maximum attempts reached');
    }

    const attemptNumber = previousAttempts.length + 1;

    const attempt = this.attemptRepository.create({
      assessment_id: assessmentId,
      user_id: userId,
      attempt_number: attemptNumber,
      status: AttemptStatus.IN_PROGRESS,
      started_at: new Date(),
    });

    return await this.attemptRepository.save(attempt);
  }

  async submitAnswer(attemptId: string, submitAnswerDto: SubmitAnswerDto, userId: string): Promise<AssessmentAnswer> {
    const attempt = await this.attemptRepository.findOne({
      where: { id: attemptId, user_id: userId, status: AttemptStatus.IN_PROGRESS },
      relations: ['assessment'],
    });

    if (!attempt) {
      throw new NotFoundException('Active attempt not found');
    }

    const question = await this.questionRepository.findOne({
      where: { id: submitAnswerDto.question_id },
      relations: ['options'],
    });

    if (!question) {
      throw new NotFoundException('Question not found');
    }

    // Check if answer already exists
    let answer = await this.answerRepository.findOne({
      where: { attempt_id: attemptId, question_id: submitAnswerDto.question_id },
    });

    if (answer) {
      // Update existing answer
      Object.assign(answer, {
        answer_data: submitAnswerDto.answer_data,
        confidence_level: submitAnswerDto.confidence_level,
        flagged_for_review: submitAnswerDto.flagged_for_review,
        time_spent_seconds: submitAnswerDto.time_spent_seconds,
        answered_at: new Date(),
      });
    } else {
      // Create new answer
      answer = this.answerRepository.create({
        attempt_id: attemptId,
        question_id: submitAnswerDto.question_id,
        answer_data: submitAnswerDto.answer_data,
        confidence_level: submitAnswerDto.confidence_level || 3,
        flagged_for_review: submitAnswerDto.flagged_for_review || false,
        time_spent_seconds: submitAnswerDto.time_spent_seconds || 0,
        answered_at: new Date(),
      });
    }

    // Grade the answer
    const gradingResult = await this.gradeAnswer(question, answer);
    answer.is_correct = gradingResult.is_correct;
    answer.points_earned = gradingResult.points_earned;
    answer.points_possible = question.points;

    return await this.answerRepository.save(answer);
  }

  async completeAssessment(attemptId: string, userId: string): Promise<AssessmentAttempt> {
    const attempt = await this.attemptRepository.findOne({
      where: { id: attemptId, user_id: userId, status: AttemptStatus.IN_PROGRESS },
      relations: ['assessment', 'answers', 'answers.question'],
    });

    if (!attempt) {
      throw new NotFoundException('Active attempt not found');
    }

    const now = new Date();
    const timeSpent = Math.floor((now.getTime() - attempt.started_at.getTime()) / 1000);

    // Calculate final score
    const totalPoints = attempt.answers.reduce((sum, answer) => sum + answer.points_earned, 0);
    const maxPoints = attempt.answers.reduce((sum, answer) => sum + answer.points_possible, 0);
    const percentage = maxPoints > 0 ? (totalPoints / maxPoints) * 100 : 0;

    const correctAnswers = attempt.answers.filter(answer => answer.is_correct).length;
    const incorrectAnswers = attempt.answers.filter(answer => !answer.is_correct).length;
    const unansweredQuestions = attempt.assessment.total_questions - attempt.answers.length;

    // Update attempt
    attempt.status = AttemptStatus.COMPLETED;
    attempt.completed_at = now;
    attempt.submitted_at = now;
    attempt.time_spent_seconds = timeSpent;
    attempt.score = totalPoints;
    attempt.percentage = percentage;
    attempt.passed = percentage >= attempt.assessment.passing_score;
    attempt.correct_answers = correctAnswers;
    attempt.incorrect_answers = incorrectAnswers;
    attempt.unanswered_questions = unansweredQuestions;

    // Generate analytics and recommendations
    attempt.analytics = this.generateAnalytics(attempt);
    attempt.recommendations = this.generateRecommendations(attempt);

    return await this.attemptRepository.save(attempt);
  }

  private canUserAttempt(assessment: Assessment, previousAttempts: AssessmentAttempt[]): boolean {
    return previousAttempts.length < assessment.max_attempts;
  }

  private async gradeAnswer(question: Question, answer: AssessmentAnswer): Promise<{ is_correct: boolean; points_earned: number }> {
    switch (question.type) {
      case QuestionType.MULTIPLE_CHOICE:
        return this.gradeMultipleChoice(question, answer);
      case QuestionType.MULTIPLE_SELECT:
        return this.gradeMultipleSelect(question, answer);
      case QuestionType.TRUE_FALSE:
        return this.gradeTrueFalse(question, answer);
      default:
        return { is_correct: false, points_earned: 0 };
    }
  }

  private gradeMultipleChoice(question: Question, answer: AssessmentAnswer): { is_correct: boolean; points_earned: number } {
    const selectedOptions = answer.answer_data?.selected_options || [];
    const correctOptions = question.options.filter(opt => opt.is_correct).map(opt => opt.id);
    
    const isCorrect = selectedOptions.length === 1 && correctOptions.includes(selectedOptions[0]);
    return {
      is_correct: isCorrect,
      points_earned: isCorrect ? question.points : 0,
    };
  }

  private gradeMultipleSelect(question: Question, answer: AssessmentAnswer): { is_correct: boolean; points_earned: number } {
    const selectedOptions = answer.answer_data?.selected_options || [];
    const correctOptions = question.options.filter(opt => opt.is_correct).map(opt => opt.id);
    
    const isCorrect = selectedOptions.length === correctOptions.length && 
                     selectedOptions.every(opt => correctOptions.includes(opt));
    
    return {
      is_correct: isCorrect,
      points_earned: isCorrect ? question.points : 0,
    };
  }

  private gradeTrueFalse(question: Question, answer: AssessmentAnswer): { is_correct: boolean; points_earned: number } {
    const selectedOptions = answer.answer_data?.selected_options || [];
    const correctOptions = question.options.filter(opt => opt.is_correct).map(opt => opt.id);
    
    const isCorrect = selectedOptions.length === 1 && correctOptions.includes(selectedOptions[0]);
    return {
      is_correct: isCorrect,
      points_earned: isCorrect ? question.points : 0,
    };
  }

  private generateAnalytics(attempt: AssessmentAttempt): any {
    // Generate detailed analytics based on attempt data
    return {
      time_per_question: attempt.answers.map(answer => answer.time_spent_seconds),
      difficulty_breakdown: this.calculateDifficultyBreakdown(attempt),
      category_breakdown: this.calculateCategoryBreakdown(attempt),
      confidence_levels: attempt.answers.map(answer => answer.confidence_level),
    };
  }

  private generateRecommendations(attempt: AssessmentAttempt): any {
    // Generate personalized recommendations based on performance
    const weakAreas = this.identifyWeakAreas(attempt);
    
    return {
      study_areas: weakAreas,
      difficulty_level: attempt.percentage < 70 ? 'review_basics' : 'advance_topics',
      next_topics: this.suggestNextTopics(attempt),
      remediation_resources: this.suggestResources(weakAreas),
    };
  }

  private calculateDifficultyBreakdown(attempt: AssessmentAttempt): any {
    // Implementation for difficulty analysis
    return {
      easy: { correct: 0, total: 0 },
      medium: { correct: 0, total: 0 },
      hard: { correct: 0, total: 0 },
    };
  }

  private calculateCategoryBreakdown(attempt: AssessmentAttempt): any {
    // Implementation for category analysis
    return {};
  }

  private identifyWeakAreas(attempt: AssessmentAttempt): string[] {
    // Implementation for identifying weak areas
    return [];
  }

  private suggestNextTopics(attempt: AssessmentAttempt): string[] {
    // Implementation for topic suggestions
    return [];
  }

  private suggestResources(weakAreas: string[]): string[] {
    // Implementation for resource suggestions
    return [];
  }
}
