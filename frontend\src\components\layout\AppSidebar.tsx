'use client';

import React from 'react';
import { 
  X,
  User,
  Stethoscope,
  Home,
  BookOpen,
  BarChart3,
  Calendar,
  Users,
  FileText,
  Award,
  Settings,
  Target,
  Activity,
  Brain,
  Heart,
  GraduationCap,
  Shield
} from 'lucide-react';
import { NavigationItemComponent } from './NavigationItem';

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  badge?: string | number;
  children?: NavigationItem[];
}

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: string;
  isAuthenticated: boolean;
}

interface AppSidebarProps {
  sidebarOpen: boolean;
  sidebarCollapsed: boolean;
  setSidebarOpen: (open: boolean) => void;
  theme: string;
  user: User;
}

export const AppSidebar: React.FC<AppSidebarProps> = ({
  sidebarOpen,
  sidebarCollapsed,
  setSidebarOpen,
  theme,
  user
}) => {
  // Navigation structure based on available routes
  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      href: '/dashboard'
    },
    {
      id: 'courses',
      label: 'Courses',
      icon: BookOpen,
      href: '/courses'
    },
    {
      id: 'schedule',
      label: 'Schedule',
      icon: Calendar,
      href: '/schedule'
    },
    {
      id: 'goals',
      label: 'Goals',
      icon: Target,
      href: '/goals'
    },
    {
      id: 'achievements',
      label: 'Achievements',
      icon: Award,
      href: '/achievements'
    },
    {
      id: 'study-groups',
      label: 'Study Groups',
      icon: Users,
      href: '/study-groups'
    }
  ];

  // Show admin section only for admin users
  if (user?.role === 'admin' || user?.role === 'instructor') {
    navigationItems.push({
      id: 'admin',
      label: 'Admin',
      icon: Settings,
      href: '/admin',
      children: [
        {
          id: 'users',
          label: 'User Management',
          icon: Users,
          href: '/admin/users'
        },
        {
          id: 'analytics',
          label: 'Analytics',
          icon: BarChart3,
          href: '/admin/analytics'
        },
        {
          id: 'settings',
          label: 'Settings',
          icon: Settings,
          href: '/admin/settings'
        }
      ]
    });
  }

  return (
    <nav className={`${sidebarOpen ? 'block' : 'hidden'} lg:block`}>
      <div className={`
        fixed inset-y-0 left-0 z-50 transform transition-all duration-300 ease-in-out
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:static lg:inset-0
        ${sidebarCollapsed && !sidebarOpen ? 'lg:w-16' : 'w-64'}
        ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}
        border-r flex flex-col
      `}>
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <Stethoscope className="h-8 w-8 text-blue-600" />
            {(!sidebarCollapsed || sidebarOpen) && (
              <div>
                <h1 className="text-xl font-bold">MedTrack</h1>
                <p className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>Medical Education</p>
              </div>
            )}
          </div>
          
          <button
            type="button"
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            aria-label="Close sidebar"
            title="Close sidebar"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto py-4">
          <div className="space-y-1">
            {navigationItems.map(item => (
              <NavigationItemComponent 
                key={item.id} 
                item={item} 
                sidebarCollapsed={sidebarCollapsed}
                sidebarOpen={sidebarOpen}
                onClick={() => { if (window.innerWidth < 1024) setSidebarOpen(false); }}
              />
            ))}
          </div>
        </nav>

        {/* Sidebar Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          {(!sidebarCollapsed || sidebarOpen) && (
            <div className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{user.name}</p>
                <p className={`text-xs truncate ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                  {user.role}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};
