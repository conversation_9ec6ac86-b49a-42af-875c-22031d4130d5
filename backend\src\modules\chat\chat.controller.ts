import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ChatService, ChatRequest, ChatResponse } from './chat.service';
import { ChatSession } from './entities/chat-session.entity';
import { ChatMessage } from './entities/chat-message.entity';

@ApiTags('chat')
@ApiBearerAuth()
@Controller('chat')
@UseGuards(JwtAuthGuard)
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post('message')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Send a message to the AI tutor' })
  @ApiResponse({
    status: 200,
    description: 'AI response generated successfully',
    type: Object,
  })
  async sendMessage(
    @Request() req,
    @Body() chatRequest: ChatRequest,
  ): Promise<ChatResponse> {
    return await this.chatService.chat(req.user.id, chatRequest);
  }

  @Get('sessions')
  @ApiOperation({ summary: 'Get user chat sessions' })
  @ApiResponse({
    status: 200,
    description: 'User sessions retrieved successfully',
    type: [ChatSession],
  })
  async getUserSessions(@Request() req): Promise<ChatSession[]> {
    return await this.chatService.getUserSessions(req.user.id);
  }

  @Get('sessions/:sessionId/messages')
  @ApiOperation({ summary: 'Get messages from a specific session' })
  @ApiResponse({
    status: 200,
    description: 'Session messages retrieved successfully',
    type: [ChatMessage],
  })
  async getSessionMessages(
    @Request() req,
    @Param('sessionId') sessionId: string,
  ): Promise<ChatMessage[]> {
    return await this.chatService.getSessionMessages(sessionId, req.user.id);
  }

  @Delete('sessions/:sessionId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a chat session' })
  @ApiResponse({
    status: 204,
    description: 'Session deleted successfully',
  })
  async deleteSession(
    @Request() req,
    @Param('sessionId') sessionId: string,
  ): Promise<void> {
    return await this.chatService.deleteSession(sessionId, req.user.id);
  }

  @Post('sessions/:sessionId/regenerate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Regenerate the last AI response' })
  @ApiResponse({
    status: 200,
    description: 'Response regenerated successfully',
  })
  async regenerateResponse(
    @Request() req,
    @Param('sessionId') sessionId: string,
  ): Promise<ChatResponse> {
    // Get the last user message and regenerate response
    const messages = await this.chatService.getSessionMessages(sessionId, req.user.id);
    const lastUserMessage = messages
      .filter(msg => msg.role === 'user')
      .pop();

    if (!lastUserMessage) {
      throw new Error('No user message found to regenerate response for');
    }

    return await this.chatService.chat(req.user.id, {
      message: lastUserMessage.content,
      sessionId,
    });
  }
}
