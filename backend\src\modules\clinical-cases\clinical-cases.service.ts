import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClinicalCase, CaseComplexity, CaseSpecialty, CaseStatus } from '../../entities/clinical-case.entity';
import { CaseAttempt, CaseAttemptStatus } from '../../entities/case-attempt.entity';
import { User } from '../../entities/user.entity';
import { Course } from '../../entities/course.entity';
import { Unit } from '../../entities/unit.entity';

export interface CreateClinicalCaseDto {
  title: string;
  description: string;
  complexity: CaseComplexity;
  specialty: CaseSpecialty;
  estimated_duration_minutes: number;
  learning_objectives: string[];
  tags: string[];
  patient_info: any;
  case_flow: any;
  diagnostic_criteria?: any;
  course_id?: string;
  unit_id?: string;
}

export interface StartCaseAttemptDto {
  case_id: string;
}

export interface UpdateCaseProgressDto {
  current_section: string;
  decision_point_id?: string;
  selected_option_id?: string;
  time_spent?: number;
}

export interface SubmitDiagnosisDto {
  differential_diagnoses: {
    diagnosis: string;
    confidence: number;
    reasoning: string;
  }[];
  final_diagnosis: string;
  diagnostic_confidence: number;
  key_findings_identified: string[];
  treatment_plan: {
    immediate_actions: string[];
    medications: string[];
    follow_up: string[];
  };
}

@Injectable()
export class ClinicalCasesService {
  constructor(
    @InjectRepository(ClinicalCase)
    private readonly clinicalCaseRepository: Repository<ClinicalCase>,
    @InjectRepository(CaseAttempt)
    private readonly caseAttemptRepository: Repository<CaseAttempt>,
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
    @InjectRepository(Unit)
    private readonly unitRepository: Repository<Unit>,
  ) {}

  async create(createClinicalCaseDto: CreateClinicalCaseDto, creatorId: string): Promise<ClinicalCase> {
    // Validate course and unit if provided
    if (createClinicalCaseDto.course_id) {
      const course = await this.courseRepository.findOne({
        where: { id: createClinicalCaseDto.course_id },
      });
      if (!course) {
        throw new NotFoundException('Course not found');
      }
    }

    if (createClinicalCaseDto.unit_id) {
      const unit = await this.unitRepository.findOne({
        where: { id: createClinicalCaseDto.unit_id },
      });
      if (!unit) {
        throw new NotFoundException('Unit not found');
      }
    }

    const clinicalCase = this.clinicalCaseRepository.create({
      ...createClinicalCaseDto,
      created_by: creatorId,
      status: CaseStatus.DRAFT,
    });

    return await this.clinicalCaseRepository.save(clinicalCase);
  }

  async findAll(filters: {
    course_id?: string;
    unit_id?: string;
    specialty?: CaseSpecialty;
    complexity?: CaseComplexity;
    status?: CaseStatus;
    search?: string;
  } = {}) {
    const queryBuilder = this.clinicalCaseRepository
      .createQueryBuilder('case')
      .leftJoinAndSelect('case.course', 'course')
      .leftJoinAndSelect('case.unit', 'unit')
      .leftJoinAndSelect('case.creator', 'creator');

    if (filters.course_id) {
      queryBuilder.andWhere('case.course_id = :course_id', { course_id: filters.course_id });
    }

    if (filters.unit_id) {
      queryBuilder.andWhere('case.unit_id = :unit_id', { unit_id: filters.unit_id });
    }

    if (filters.specialty) {
      queryBuilder.andWhere('case.specialty = :specialty', { specialty: filters.specialty });
    }

    if (filters.complexity) {
      queryBuilder.andWhere('case.complexity = :complexity', { complexity: filters.complexity });
    }

    if (filters.status) {
      queryBuilder.andWhere('case.status = :status', { status: filters.status });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(case.title ILIKE :search OR case.description ILIKE :search OR case.tags && ARRAY[:search])',
        { search: `%${filters.search}%` }
      );
    }

    queryBuilder.orderBy('case.created_at', 'DESC');

    return await queryBuilder.getMany();
  }

  async findOne(id: string, userId?: string): Promise<ClinicalCase> {
    const clinicalCase = await this.clinicalCaseRepository.findOne({
      where: { id },
      relations: ['course', 'unit', 'creator', 'attempts'],
    });

    if (!clinicalCase) {
      throw new NotFoundException('Clinical case not found');
    }

    // If user is provided, get their attempt history
    if (userId) {
      const userAttempts = await this.caseAttemptRepository.find({
        where: { clinical_case_id: id, user_id: userId },
        order: { created_at: 'DESC' },
      });

      (clinicalCase as any).user_attempts = userAttempts;
      (clinicalCase as any).current_attempt = userAttempts.find(
        attempt => attempt.status === CaseAttemptStatus.IN_PROGRESS
      );
    }

    return clinicalCase;
  }

  async startAttempt(caseId: string, userId: string): Promise<CaseAttempt> {
    const clinicalCase = await this.clinicalCaseRepository.findOne({
      where: { id: caseId, status: CaseStatus.PUBLISHED },
    });

    if (!clinicalCase) {
      throw new NotFoundException('Clinical case not found or not available');
    }

    // Check if user has an active attempt
    const activeAttempt = await this.caseAttemptRepository.findOne({
      where: { 
        clinical_case_id: caseId, 
        user_id: userId, 
        status: CaseAttemptStatus.IN_PROGRESS 
      },
    });

    if (activeAttempt) {
      return activeAttempt; // Return existing active attempt
    }

    // Create new attempt
    const attempt = this.caseAttemptRepository.create({
      clinical_case_id: caseId,
      user_id: userId,
      status: CaseAttemptStatus.IN_PROGRESS,
      started_at: new Date(),
      max_score: this.calculateMaxScore(clinicalCase),
      progress: {
        current_section: clinicalCase.case_flow.sections[0]?.id || '',
        completed_sections: [],
        unlocked_sections: clinicalCase.case_flow.sections
          .filter(section => section.is_unlocked_initially)
          .map(section => section.id),
        decisions_made: [],
        section_times: [],
      },
    });

    return await this.caseAttemptRepository.save(attempt);
  }

  async updateProgress(
    attemptId: string, 
    updateProgressDto: UpdateCaseProgressDto, 
    userId: string
  ): Promise<CaseAttempt> {
    const attempt = await this.caseAttemptRepository.findOne({
      where: { 
        id: attemptId, 
        user_id: userId, 
        status: CaseAttemptStatus.IN_PROGRESS 
      },
      relations: ['clinical_case'],
    });

    if (!attempt) {
      throw new NotFoundException('Active attempt not found');
    }

    const progress = attempt.progress || {
      current_section: '',
      completed_sections: [],
      unlocked_sections: [],
      decisions_made: [],
      section_times: [],
    };

    // Update current section
    if (updateProgressDto.current_section) {
      progress.current_section = updateProgressDto.current_section;
    }

    // Handle decision point
    if (updateProgressDto.decision_point_id && updateProgressDto.selected_option_id) {
      const decision = {
        decision_point_id: updateProgressDto.decision_point_id,
        selected_option_id: updateProgressDto.selected_option_id,
        timestamp: new Date(),
        points_earned: this.calculateDecisionPoints(
          attempt.clinical_case,
          updateProgressDto.decision_point_id,
          updateProgressDto.selected_option_id
        ),
      };

      progress.decisions_made.push(decision);

      // Check for section unlocks
      const newUnlocks = this.checkSectionUnlocks(
        attempt.clinical_case,
        updateProgressDto.decision_point_id,
        updateProgressDto.selected_option_id
      );
      progress.unlocked_sections.push(...newUnlocks);

      // Update score
      attempt.score += decision.points_earned;
    }

    // Update time tracking
    if (updateProgressDto.time_spent) {
      const existingTimeIndex = progress.section_times.findIndex(
        st => st.section_id === updateProgressDto.current_section
      );

      if (existingTimeIndex >= 0) {
        progress.section_times[existingTimeIndex].time_spent += updateProgressDto.time_spent;
      } else {
        progress.section_times.push({
          section_id: updateProgressDto.current_section,
          time_spent: updateProgressDto.time_spent,
        });
      }

      attempt.time_spent_seconds += updateProgressDto.time_spent;
    }

    attempt.progress = progress;
    attempt.percentage = (attempt.score / attempt.max_score) * 100;

    return await this.caseAttemptRepository.save(attempt);
  }

  async submitDiagnosis(
    attemptId: string,
    submitDiagnosisDto: SubmitDiagnosisDto,
    userId: string
  ): Promise<CaseAttempt> {
    const attempt = await this.caseAttemptRepository.findOne({
      where: { 
        id: attemptId, 
        user_id: userId, 
        status: CaseAttemptStatus.IN_PROGRESS 
      },
      relations: ['clinical_case'],
    });

    if (!attempt) {
      throw new NotFoundException('Active attempt not found');
    }

    // Save clinical reasoning
    attempt.clinical_reasoning = submitDiagnosisDto;

    // Calculate diagnostic accuracy score
    const diagnosticScore = this.calculateDiagnosticScore(
      attempt.clinical_case,
      submitDiagnosisDto
    );

    attempt.score += diagnosticScore;
    attempt.percentage = (attempt.score / attempt.max_score) * 100;

    return await this.caseAttemptRepository.save(attempt);
  }

  async completeAttempt(attemptId: string, userId: string): Promise<CaseAttempt> {
    const attempt = await this.caseAttemptRepository.findOne({
      where: { 
        id: attemptId, 
        user_id: userId, 
        status: CaseAttemptStatus.IN_PROGRESS 
      },
      relations: ['clinical_case'],
    });

    if (!attempt) {
      throw new NotFoundException('Active attempt not found');
    }

    // Generate comprehensive feedback
    const feedback = this.generateFeedback(attempt);
    const analytics = this.generateAnalytics(attempt);

    attempt.status = CaseAttemptStatus.COMPLETED;
    attempt.completed_at = new Date();
    attempt.feedback = feedback;
    attempt.analytics = analytics;

    return await this.caseAttemptRepository.save(attempt);
  }

  private calculateMaxScore(clinicalCase: ClinicalCase): number {
    let maxScore = 0;

    // Points from decision points
    clinicalCase.case_flow.decision_points.forEach(dp => {
      const maxOptionPoints = Math.max(...dp.options.map(opt => opt.points || 0));
      maxScore += maxOptionPoints;
    });

    // Points from sections
    clinicalCase.case_flow.sections.forEach(section => {
      maxScore += section.points || 0;
    });

    // Diagnostic accuracy points (standard 100 points)
    maxScore += 100;

    return maxScore;
  }

  private calculateDecisionPoints(
    clinicalCase: ClinicalCase,
    decisionPointId: string,
    selectedOptionId: string
  ): number {
    const decisionPoint = clinicalCase.case_flow.decision_points.find(
      dp => dp.id === decisionPointId
    );

    if (!decisionPoint) return 0;

    const selectedOption = decisionPoint.options.find(
      opt => opt.id === selectedOptionId
    );

    return selectedOption?.points || 0;
  }

  private checkSectionUnlocks(
    clinicalCase: ClinicalCase,
    decisionPointId: string,
    selectedOptionId: string
  ): string[] {
    const decisionPoint = clinicalCase.case_flow.decision_points.find(
      dp => dp.id === decisionPointId
    );

    if (!decisionPoint) return [];

    const selectedOption = decisionPoint.options.find(
      opt => opt.id === selectedOptionId
    );

    return selectedOption?.unlocks_sections || [];
  }

  private calculateDiagnosticScore(
    clinicalCase: ClinicalCase,
    diagnosis: SubmitDiagnosisDto
  ): number {
    if (!clinicalCase.diagnostic_criteria) return 0;

    let score = 0;

    // Check final diagnosis accuracy
    if (diagnosis.final_diagnosis === clinicalCase.diagnostic_criteria.final_diagnosis) {
      score += 50; // 50 points for correct final diagnosis
    }

    // Check differential diagnoses
    const correctDifferentials = clinicalCase.diagnostic_criteria.differential_diagnoses
      .map(dd => dd.diagnosis);
    
    const identifiedCorrectDifferentials = diagnosis.differential_diagnoses
      .filter(dd => correctDifferentials.includes(dd.diagnosis));

    score += identifiedCorrectDifferentials.length * 10; // 10 points per correct differential

    // Check key findings identification
    const identifiedKeyFindings = diagnosis.key_findings_identified
      .filter(finding => clinicalCase.diagnostic_criteria.key_findings.includes(finding));

    score += identifiedKeyFindings.length * 5; // 5 points per key finding

    return Math.min(score, 100); // Cap at 100 points
  }

  private generateFeedback(attempt: CaseAttempt): any {
    // Generate comprehensive feedback based on performance
    return {
      overall_performance: attempt.percentage >= 80 ? 'Excellent' : 
                          attempt.percentage >= 70 ? 'Good' : 
                          attempt.percentage >= 60 ? 'Satisfactory' : 'Needs Improvement',
      strengths: this.identifyStrengths(attempt),
      areas_for_improvement: this.identifyWeaknesses(attempt),
      specific_feedback: this.generateSectionFeedback(attempt),
      recommendations: this.generateRecommendations(attempt),
    };
  }

  private generateAnalytics(attempt: CaseAttempt): any {
    return {
      decision_accuracy: this.calculateDecisionAccuracy(attempt),
      time_efficiency: this.calculateTimeEfficiency(attempt),
      clinical_reasoning_score: this.calculateClinicalReasoningScore(attempt),
      knowledge_gaps: this.identifyKnowledgeGaps(attempt),
      learning_objectives_met: this.checkLearningObjectives(attempt),
      difficulty_appropriate: this.assessDifficultyLevel(attempt),
    };
  }

  private identifyStrengths(attempt: CaseAttempt): string[] {
    // Implementation for identifying strengths
    return [];
  }

  private identifyWeaknesses(attempt: CaseAttempt): string[] {
    // Implementation for identifying weaknesses
    return [];
  }

  private generateSectionFeedback(attempt: CaseAttempt): any[] {
    // Implementation for section-specific feedback
    return [];
  }

  private generateRecommendations(attempt: CaseAttempt): any {
    // Implementation for generating recommendations
    return {
      study_topics: [],
      resources: [],
      next_cases: [],
    };
  }

  private calculateDecisionAccuracy(attempt: CaseAttempt): number {
    // Implementation for decision accuracy calculation
    return 0;
  }

  private calculateTimeEfficiency(attempt: CaseAttempt): number {
    // Implementation for time efficiency calculation
    return 0;
  }

  private calculateClinicalReasoningScore(attempt: CaseAttempt): number {
    // Implementation for clinical reasoning score
    return 0;
  }

  private identifyKnowledgeGaps(attempt: CaseAttempt): string[] {
    // Implementation for knowledge gap identification
    return [];
  }

  private checkLearningObjectives(attempt: CaseAttempt): string[] {
    // Implementation for learning objectives assessment
    return [];
  }

  private assessDifficultyLevel(attempt: CaseAttempt): boolean {
    // Implementation for difficulty assessment
    return true;
  }
}
